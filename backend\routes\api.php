<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\FacilityController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\DashboardController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// API versioning
Route::prefix('v1')->group(function () {
    
    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::get('me', [AuthController::class, 'me']);
    });

    // Protected routes
    Route::middleware('auth:api')->group(function () {
        
        // Dashboard
        Route::get('dashboard/stats', [DashboardController::class, 'getStats']);
        
        // Patient records (doctors and admins only)
        Route::middleware('role:doctor,admin')->group(function () {
            Route::apiResource('patients', PatientController::class);
            Route::post('patients/{id}/visit-notes', [PatientController::class, 'addVisitNote']);
            Route::post('patients/{id}/prescriptions', [PatientController::class, 'addPrescription']);
            Route::post('patients/{id}/documents', [PatientController::class, 'uploadDocument']);
        });
        
        // Appointments
        Route::apiResource('appointments', AppointmentController::class);
        Route::patch('appointments/{id}/status', [AppointmentController::class, 'updateStatus']);
        Route::get('appointments/calendar/{date}', [AppointmentController::class, 'getCalendar']);
        
        // Health facilities
        Route::apiResource('facilities', FacilityController::class);
        Route::get('facilities/search/{query}', [FacilityController::class, 'search']);
        
        // Billing (doctors and admins only)
        Route::middleware('role:doctor,admin')->group(function () {
            Route::apiResource('billing', BillingController::class);
            Route::post('billing/{id}/approve', [BillingController::class, 'approve']);
            Route::get('billing/{id}/invoice', [BillingController::class, 'generateInvoice']);
        });
        
        // Admin routes
        Route::middleware('role:admin')->group(function () {
            Route::prefix('admin')->group(function () {
                Route::get('stats', [AdminController::class, 'getStats']);
                Route::get('users', [AdminController::class, 'getUsers']);
                Route::get('activity-logs', [AdminController::class, 'getActivityLogs']);
                Route::get('system-health', [AdminController::class, 'getSystemHealth']);
                Route::post('users/{id}/verify', [AdminController::class, 'verifyUser']);
                Route::post('users/{id}/suspend', [AdminController::class, 'suspendUser']);
            });
        });
        
        // eVerification
        Route::get('verify/{qrCode}', [AuthController::class, 'verifyQRCode']);
        Route::get('profile/qr', [AuthController::class, 'getQRCode']);
        
        // Profile management
        Route::get('profile', [AuthController::class, 'me']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('profile/photo', [AuthController::class, 'uploadPhoto']);
    });
});

// Health check endpoint
Route::get('health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});
