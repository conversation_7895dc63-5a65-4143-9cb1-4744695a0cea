"use client"

import type React from "react"
import { Navigate, useLocation } from "react-router-dom"
import { useAuth } from "../contexts/AuthContext"
import LoadingSpinner from "./LoadingSpinner"

interface ProtectedRouteProps {
  children: React.ReactNode
  roles?: string[]
  fallback?: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, roles = [], fallback }) => {
  const { state } = useAuth()
  const location = useLocation()

  // Show loading spinner while checking authentication
  if (state.loading) {
    return <LoadingSpinner fullScreen text="Authenticating..." />
  }

  // Redirect to login if not authenticated
  if (!state.token || !state.user) {
    return <Navigate to="/login" state={{ from: location.pathname }} replace />
  }

  // Check role permissions
  if (roles.length > 0 && !roles.includes(state.user.role)) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <Navigate
        to="/dashboard"
        state={{
          error: "You don't have permission to access this page.",
          from: location.pathname,
        }}
        replace
      />
    )
  }

  return <>{children}</>
}

export default ProtectedRoute
